/* 合并的主样式文件 - PS图片转换插件 */

/* ===== CSS变量定义 ===== */
:root {
    /* Primary colors */
    --primary-color: #2680EB;
    --primary-hover: #1473E6;
    --secondary-color: #6E6E6E;
    --background-color: #2D2D2D;
    --panel-color: #323232;
    --text-color: #E6E6E6;
    --border-color: #464646;
    --hover-color: #444444;
    --disabled-color: #5E5E5E;
    --success-color: #2D9D78;
    --warning-color: #E68619;
    --error-color: #E34850;

    /* Dimensions and spacing */
    --border-radius: 4px;
    --shadow-small: 0 2px 4px rgba(0, 0, 0, 0.2);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.3);
    --transition-normal: all 0.2s ease-in-out;
    --padding-standard: 8px;
    --padding-small: 4px;
    --min-panel-width: 220px;
    --min-panel-height: 400px;
    --section-spacing: 6px;

    /* Gradient effects */
    --gradient-primary: linear-gradient(135deg, #2680EB, #1473E6);
    --gradient-secondary: linear-gradient(135deg, rgba(70, 70, 70, 0.8), rgba(55, 55, 55, 0.8));
    --gradient-feature: linear-gradient(145deg, rgba(60, 60, 60, 0.7), rgba(45, 45, 45, 0.7));
    --gradient-header: linear-gradient(145deg, rgba(50, 50, 50, 0.8), rgba(40, 40, 40, 0.8));
    --gradient-container: linear-gradient(180deg, rgba(50, 50, 50, 1) 0%, rgba(40, 40, 40, 1) 100%);
}

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    min-height: var(--min-panel-height);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
    color: var(--text-color);
    background-color: var(--background-color);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow: hidden;
    min-width: var(--min-panel-width);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ===== 容器和布局样式 ===== */
.container {
    height: 100%;
    min-height: var(--min-panel-height);
    overflow-y: auto;
    padding: var(--padding-standard);
    display: flex;
    flex-direction: column;
    gap: var(--section-spacing);
    width: 100%;
    min-width: var(--min-panel-width);
    position: relative;
    background: var(--gradient-container);
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--section-spacing);
    width: 100%;
    min-height: 200px;
    overflow-y: auto;
    padding: 4px 0;
}

/* 分隔线 */
.divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 4px 0;
    width: 100%;
}

.divider.medium {
    margin: 6px 0;
}

/* 段落样式 */
.description {
    color: #B8B8B8;
    font-size: 13px;
    margin-top: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* ===== 按钮样式 ===== */
.sp-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid transparent;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 32px;
    box-shadow: var(--shadow-small);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    position: relative;
    line-height: 1.1;
}

.sp-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sp-button:hover::after {
    opacity: 1;
}

.sp-button:active {
    transform: scale(0.98);
}

.sp-button.primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(38, 128, 235, 0.3);
}

.sp-button.primary:hover {
    background: linear-gradient(135deg, #1473E6, #0d66d0);
    box-shadow: 0 4px 12px rgba(38, 128, 235, 0.5);
    transform: translateY(-1px);
}

.sp-button.secondary {
    background: var(--gradient-secondary);
    border: 1px solid rgba(255, 255, 255, 0.05);
    color: var(--text-color);
}

.sp-button.secondary:hover {
    background: linear-gradient(135deg, rgba(80, 80, 80, 0.8), rgba(65, 65, 65, 0.8));
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transform: translateY(-1px);
}

.sp-button.quiet {
    background-color: transparent;
    color: var(--text-color);
    padding: 3px 6px;
    min-height: 22px;
    box-shadow: none;
}

.sp-button.quiet:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sp-button.small {
    font-size: 11px;
    padding: 3px 6px;
    min-height: 22px;
}

.sp-button:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.sp-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.button-icon {
    margin-right: 6px;
    font-size: 14px;
    flex-shrink: 0;
}

/* ===== 下拉菜单样式 ===== */
.sp-dropdown {
    padding: 4px 24px 4px 8px;
    border-radius: 6px;
    background-color: rgba(60, 60, 60, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="%23E6E6E6" d="M6 6L0 0h12z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 8px center;
    color: var(--text-color);
    font-size: 13px;
    appearance: none;
    cursor: pointer;
    min-width: 120px;
    max-width: 100%;
}

.sp-dropdown:hover {
    background-color: rgba(70, 70, 70, 0.8);
    border-color: rgba(38, 128, 235, 0.5);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 0 4px rgba(38, 128, 235, 0.4);
}

.sp-dropdown:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== 区块样式 ===== */
.section {
    padding: 4px 8px;
    background-color: var(--panel-color);
    border-radius: 8px;
    box-shadow: var(--shadow-small);
    margin-bottom: 4px;
    width: 100%;
    flex-shrink: 0;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.section:hover {
    box-shadow: var(--shadow-medium);
    border-color: rgba(255, 255, 255, 0.1);
    background-color: rgba(255, 255, 255, 0.03);
}

.feature-section {
    background: var(--gradient-feature) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.feature-section::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, rgba(38, 128, 235, 0.05) 0%, rgba(38, 128, 235, 0) 70%);
    z-index: -1;
    border-radius: 12px;
    filter: blur(8px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
}

.feature-section:hover::before {
    opacity: 1;
    background: linear-gradient(45deg, rgba(38, 128, 235, 0.1) 0%, rgba(38, 128, 235, 0) 70%);
}

.feature-section:first-child {
    margin-top: 8px;
    border-top: 2px solid rgba(38, 128, 235, 0.4);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    flex-wrap: nowrap;
    position: relative;
    overflow: hidden;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right,
        rgba(255, 255, 255, 0),
        rgba(255, 255, 255, 0.1),
        rgba(255, 255, 255, 0));
}

.section h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background: linear-gradient(135deg, #ffffff, #b8b8b8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section h3 {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    padding-left: 14px;
    line-height: 1.1;
}

.section h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(38, 128, 235, 0.6);
}

.section h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #B8B8B8;
}

.header-section {
    background: var(--gradient-header);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 8px;
    text-align: center;
    padding: 8px;
    margin-bottom: 8px;
    position: relative;
    overflow: hidden;
}

.header-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right,
        rgba(255, 255, 255, 0),
        rgba(255, 255, 255, 0.1),
        rgba(255, 255, 255, 0));
}

.language-options {
    display: flex;
    align-items: center;
    background-color: rgba(40, 40, 40, 0.6);
    border-radius: 6px;
    padding: 6px 8px;
    margin-top: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    flex-wrap: wrap;
    gap: 8px;
}

.language-options label {
    font-weight: 500;
    color: #b8b8b8;
    margin-right: 8px;
    font-size: 12px;
    white-space: nowrap;
}

.button-group {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.button-group .sp-button {
    flex: 1;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: 500;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 4px;
    user-select: none;
    font-size: 12px;
    transition: var(--transition-normal);
}

.checkbox-label:hover {
    color: #FFFFFF;
}

.checkbox-label input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 4px;
    background-color: rgba(60, 60, 60, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    margin-right: 6px;
    flex-shrink: 0;
    position: relative;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"]:checked {
    background: linear-gradient(135deg, #2680EB, #1473E6);
    border-color: transparent;
}

.checkbox-label input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-label:hover input[type="checkbox"]:not(:checked) {
    background-color: rgba(70, 70, 70, 0.8);
    border-color: rgba(38, 128, 235, 0.5);
}

.info-box {
    background-color: rgba(38, 128, 235, 0.1);
    border: 1px solid rgba(38, 128, 235, 0.3);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    margin: 8px 0;
    font-size: 12px;
    color: #B8B8B8;
}

.progress-bar {
    height: 3px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 6px;
    width: 100%;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(to right, #2680EB, #1473E6);
    width: 0%;
    transition: width 0.3s ease;
}

.status-section {
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 6px;
    padding: 6px;
    margin-top: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.status-text {
    font-size: 12px;
    color: #b8b8b8;
    text-align: center;
    margin: 0;
    transition: var(--transition-normal);
}

.advanced-options {
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 6px;
    padding: 8px;
    margin-top: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    animation: fadeIn 0.3s ease;
}

.option-group {
    margin-bottom: 4px;
}

.option-group:last-child {
    margin-bottom: 0;
}

.option-group label {
    display: block;
    margin-bottom: 2px;
    font-size: 12px;
    color: #B8B8B8;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 3px;
    border-radius: 2px;
    background: linear-gradient(to right, rgba(38, 128, 235, 0.3), rgba(38, 128, 235, 0.1));
    outline: none;
    transition: var(--transition-normal);
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2680EB, #1473E6);
    box-shadow: 0 0 8px rgba(38, 128, 235, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    cursor: pointer;
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    background: linear-gradient(135deg, #1473E6, #0d66d0);
}

.slider-value {
    min-width: 24px;
    text-align: right;
    font-size: 13px;
    color: #B8B8B8;
}

.footer {
    background: var(--gradient-header);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 6px;
    margin-top: 8px;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right,
        rgba(255, 255, 255, 0),
        rgba(255, 255, 255, 0.1),
        rgba(255, 255, 255, 0));
}

.version {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    padding: 2px 6px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    font-size: 11px;
    color: #888888;
}

.version:hover {
    background-color: rgba(38, 128, 235, 0.2);
    color: #ffffff;
}

.action-buttons {
    display: flex;
    justify-content: center;
    margin-top: 8px;
    gap: 8px;
}

.action-buttons .sp-button {
    min-width: 120px;
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 600;
}

.action-buttons .sp-button .button-icon {
    font-size: 14px;
    margin-right: 6px;
}

/* 特殊按钮样式 */
#extractText {
    background: linear-gradient(135deg, #2680EB, #1473E6);
    box-shadow: 0 3px 10px rgba(38, 128, 235, 0.4);
    border: none;
    transition: all 0.3s ease;
    transform-origin: center;
}

#extractText:hover {
    background: linear-gradient(135deg, #1473E6, #0d66d0);
    box-shadow: 0 5px 15px rgba(38, 128, 235, 0.6);
    transform: translateY(-2px) scale(1.02);
}

#extractText:active {
    transform: translateY(1px) scale(0.98);
    box-shadow: 0 2px 8px rgba(38, 128, 235, 0.3);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 对话框 */
.dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.dialog-content {
    background-color: var(--panel-color);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 300px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-medium);
    animation: dialogFadeIn 0.2s ease-out;
}

@keyframes dialogFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid var(--border-color);
}

.dialog-header h3 {
    font-size: 14px;
    margin: 0;
}

.dialog-body {
    padding: 12px;
    max-height: 60vh;
    overflow-y: auto;
}

.dialog-footer {
    padding: 8px 12px;
    display: flex;
    justify-content: flex-end;
    gap: 6px;
    border-top: 1px solid var(--border-color);
}

.close-button {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 18px;
    cursor: pointer;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    padding: 0;
    line-height: 1;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.settings-section {
    margin-bottom: 16px;
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section h4 {
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
    flex-shrink: 0;
}

.help-icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: #B8B8B8;
    font-size: 11px;
    cursor: help;
}

.tooltip .tooltip-text {
    visibility: hidden;
    background-color: #444;
    color: #fff;
    text-align: center;
    border-radius: var(--border-radius);
    padding: 6px 10px;
    position: absolute;
    z-index: 1;
    width: 180px;
    bottom: 125%;
    left: 50%;
    margin-left: -90px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 11px;
    box-shadow: var(--shadow-medium);
    line-height: 1.4;
    pointer-events: none;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #444 transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* 工具类 */
.mt-8 {
    margin-top: 8px;
}

.flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.align-center {
    align-items: center;
}

/* 按钮容器样式 */
.action-buttons {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 6px;
    width: 100%;
}

.action-buttons .sp-button {
    min-width: 90px;
}

/* 添加加载状态样式 */
.sp-button.loading {
    opacity: 0.8;
    position: relative;
}

.loading-spinner {
    display: inline-block;
    width: 10px;
    height: 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 6px;
    flex-shrink: 0;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 添加状态文本样式 */
.status-text.success {
    color: var(--success-color);
}

.status-text.error {
    color: var(--error-color);
}

/* 添加禁用按钮样式 */
.sp-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* 添加确认对话框样式 */
.confirm-dialog .dialog-content {
    max-width: 280px;
}

.confirm-dialog .dialog-body {
    text-align: center;
    padding: 16px 12px;
}

/* 动画效果 */
.dialog-content {
    transition: opacity 0.2s ease, transform 0.2s ease;
}

/* 添加响应式交互反馈 */
.sp-button:active {
    transform: scale(0.98);
}

.section {
    transition: background-color 0.3s ease;
}

.section:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

/* 错误状态样式 */
.connection-error {
    font-size: 11px;
    color: var(--error-color);
    background-color: rgba(227, 72, 80, 0.1);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    margin-top: 4px;
    text-align: center;
}

/* 压缩模式 - 当窗口高度很小时 */
@media (max-height: 500px) {
    :root {
        --padding-standard: 4px;
        --padding-small: 4px;
        --section-spacing: 4px;
    }

    .container {
        padding: var(--padding-standard);
        gap: var(--section-spacing);
    }

    .section {
        padding: var(--padding-small);
        margin-bottom: var(--section-spacing);
    }

    .section-header {
        margin-bottom: 4px;
    }

    .divider {
        margin: 2px 0;
    }

    .divider.medium {
        margin: 3px 0;
    }

    .language-options {
        margin-bottom: 4px;
    }

    .batch-options {
        margin-bottom: 4px;
    }

    .header-section {
        padding: 4px;
    }

    .advanced-options {
        margin-top: 4px;
        padding: 4px;
    }

    .option-group {
        margin-bottom: 4px;
    }
}

/* 超窄模式 */
@media (max-width: 280px) {
    :root {
        --padding-standard: 6px;
        --padding-small: 4px;
    }

    .container {
        padding: var(--padding-standard);
        gap: 6px;
    }

    .section {
        padding: var(--padding-small);
    }

    .section h3 {
        font-size: 13px;
    }

    .button-group {
        flex-direction: column;
    }

    .button-group .sp-button {
        width: 100%;
    }

    .language-options {
        flex-direction: column;
        align-items: flex-start;
    }

    .sp-button {
        padding: 4px 8px;
        font-size: 12px;
    }

    .header-section {
        padding: 6px;
    }

    .dialog-content {
        max-width: 90%;
    }
}

/* 中等窄度面板优化 */
@media (min-width: 281px) and (max-width: 360px) {
    .container {
        padding: 8px;
    }

    .button-group .sp-button {
        padding: 5px 8px;
        font-size: 12px;
    }

    .sp-dropdown {
        min-width: 80px;
    }
}

/* 紧凑模式样式 */
html.compact-mode .container {
    gap: 2px;
}

html.compact-mode .section {
    margin-bottom: 2px;
    padding: 4px;
}

html.compact-mode .section-header {
    margin-bottom: 4px;
}

html.compact-mode .header-section {
    padding: 4px;
}

html.compact-mode .language-options {
    margin-bottom: 4px;
}

html.compact-mode .batch-options {
    margin-bottom: 4px;
}

html.compact-mode .advanced-options {
    margin-top: 4px;
    padding: 4px;
}

html.compact-mode .option-group {
    margin-bottom: 4px;
}

html.compact-mode .checkbox-label {
    margin-bottom: 2px;
}

html.compact-mode .sp-button {
    min-height: 24px;
    padding: 3px 8px;
}

html.compact-mode .status-section {
    padding: 3px 6px;
}

html.compact-mode .footer {
    padding: 3px;
}

/* ===== 动画效果 ===== */
.main-content {
    animation: slideInUp 0.5s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.status-text.animate-in {
    animation: status-text-fade-in 0.3s ease-out;
}

@keyframes status-text-fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 隐藏批量处理 ===== */
.batch-options,
html.compact-mode .batch-options {
    display: none !important;
}