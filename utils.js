// 合并的工具类文件 - PS图片转换插件

/* ===== 日志工具 ===== */
const LogLevel = {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3
};

const LogLevelName = {
    [LogLevel.ERROR]: "ERROR",
    [LogLevel.WARN]: "WARN",
    [LogLevel.INFO]: "INFO",
    [LogLevel.DEBUG]: "DEBUG"
};

class Logger {
    constructor() {
        this.logEntries = [];
        this.maxLogEntries = 1000;
    }

    getLogLevel() {
        return LogLevel.ERROR; // 简化配置
    }

    isDebugEnabled() {
        return false; // 简化配置
    }

    error(message, error = null) {
        this._log(LogLevel.ERROR, message, error);
    }

    warn(message, data = null) {
        this._log(LogLevel.WARN, message, data);
    }

    info(message, data = null) {
        this._log(LogLevel.INFO, message, data);
    }

    debug(message, data = null) {
        this._log(LogLevel.DEBUG, message, data);
    }

    async measurePerformance(label, callback) {
        const startTime = performance.now();
        try {
            return await callback();
        } finally {
            const endTime = performance.now();
            const duration = endTime - startTime;
            console.log(`性能 [${label}]: ${duration.toFixed(2)}ms`);
            this.debug(`性能测量 [${label}]`, { duration: duration.toFixed(2) + "ms" });
        }
    }

    _log(level, message, data) {
        if (!this.isDebugEnabled() && level > LogLevel.ERROR) {
            return;
        }

        if (level > this.getLogLevel()) {
            return;
        }

        const entry = {
            timestamp: new Date(),
            level: level,
            levelName: LogLevelName[level],
            message: message,
            data: data
        };

        this.logEntries.push(entry);

        if (this.logEntries.length > this.maxLogEntries) {
            this.logEntries.shift();
        }

        const logMethod = level === LogLevel.ERROR ? console.error :
                         level === LogLevel.WARN ? console.warn :
                         level === LogLevel.INFO ? console.info :
                         console.debug;

        const timestamp = entry.timestamp.toISOString().replace('T', ' ').substr(0, 19);
        const prefix = `[${timestamp}] [${entry.levelName}]`;

        if (data) {
            logMethod(`${prefix} ${message}`, data);
        } else {
            logMethod(`${prefix} ${message}`);
        }
    }

    getLogEntries() {
        return [...this.logEntries];
    }

    clearLogs() {
        this.logEntries = [];
    }

    exportLogsAsText() {
        return this.logEntries.map(entry => {
            const timestamp = entry.timestamp.toISOString().replace('T', ' ').substr(0, 19);
            let line = `[${timestamp}] [${entry.levelName}] ${entry.message}`;

            if (entry.data) {
                if (entry.data instanceof Error) {
                    line += `\nError: ${entry.data.message}`;
                    if (entry.data.stack) {
                        line += `\nStack: ${entry.data.stack}`;
                    }
                } else {
                    try {
                        line += `\nData: ${JSON.stringify(entry.data)}`;
                    } catch (e) {
                        line += `\nData: [无法序列化]`;
                    }
                }
            }

            return line;
        }).join('\n\n');
    }
}

const logger = new Logger();

/* ===== 错误处理工具 ===== */
const ErrorType = {
    VALIDATION: "VALIDATION",
    PHOTOSHOP_API: "PHOTOSHOP_API",
    OCR: "OCR",
    IMAGE_PROCESSING: "IMAGE_PROCESSING",
    NETWORK: "NETWORK",
    SYSTEM: "SYSTEM",
    UNKNOWN: "UNKNOWN"
};

class PluginError extends Error {
    constructor(message, type = ErrorType.UNKNOWN, originalError = null) {
        super(message);
        this.name = "PluginError";
        this.type = type;
        this.originalError = originalError;
    }
}

class ErrorHandler {
    handleError(error, showUI = true) {
        const normalizedError = this._normalizeError(error);
        logger.error(normalizedError.message, normalizedError);
        const userMessage = this._getUserFriendlyMessage(normalizedError);

        if (showUI) {
            uiUtils.showError(userMessage);
        }

        return {
            success: false,
            error: normalizedError,
            userMessage
        };
    }

    createValidationError(message) {
        return new PluginError(message, ErrorType.VALIDATION);
    }

    createPhotoshopError(message, originalError = null) {
        return new PluginError(message, ErrorType.PHOTOSHOP_API, originalError);
    }

    createOCRError(message, originalError = null) {
        return new PluginError(message, ErrorType.OCR, originalError);
    }

    createImageProcessingError(message, originalError = null) {
        return new PluginError(message, ErrorType.IMAGE_PROCESSING, originalError);
    }

    _normalizeError(error) {
        if (error instanceof PluginError) {
            return error;
        }

        if (error.name === "PhotoshopError") {
            return new PluginError(error.message, ErrorType.PHOTOSHOP_API, error);
        }

        if (error.name === "NetworkError") {
            return new PluginError(error.message, ErrorType.NETWORK, error);
        }

        return new PluginError(error.message || "发生未知错误", ErrorType.UNKNOWN, error);
    }

    _getUserFriendlyMessage(error) {
        switch (error.type) {
            case ErrorType.VALIDATION:
                return error.message;
            case ErrorType.PHOTOSHOP_API:
                return "Photoshop操作失败，请重试。如果问题持续存在，请重启Photoshop。";
            case ErrorType.OCR:
                return "文字识别失败，请检查图像质量或尝试其他语言设置。";
            case ErrorType.IMAGE_PROCESSING:
                return "图像处理失败，请检查图像是否有效。";
            case ErrorType.NETWORK:
                return "网络连接失败，请检查网络连接后重试。";
            case ErrorType.SYSTEM:
                return "系统错误，请检查系统资源是否充足。";
            default:
                return "操作失败，请重试。如果问题持续存在，请联系支持团队。";
        }
    }

    wrapAsync(fn, showUI = true) {
        return async (...args) => {
            try {
                return await fn(...args);
            } catch (error) {
                return this.handleError(error, showUI);
            }
        };
    }

    assert(condition, message) {
        if (!condition) {
            throw this.createValidationError(message);
        }
    }
}

const errorHandler = new ErrorHandler();

/* ===== UI工具 ===== */
class UIUtils {
    constructor() {
        this.elements = {
            progressBar: null,
            progressFill: null,
            statusText: null
        };
        this.updateTimer = null;
        this.updateDelay = 100;
    }

    initialize() {
        this.elements.progressBar = document.getElementById("progressBar");
        this.elements.progressFill = document.querySelector(".progress-fill");
        this.elements.statusText = document.getElementById("statusText");
        this._initializeTooltips();
        this._initializeThemeSupport();
    }

    updateStatus(message, progress = null) {
        if (this.updateTimer) {
            clearTimeout(this.updateTimer);
        }

        this.updateTimer = setTimeout(() => {
            this._updateStatusImpl(message, progress);
        }, this.updateDelay);
    }

    _updateStatusImpl(message, progress) {
        if (this.elements.statusText) {
            this.elements.statusText.textContent = message;
        }

        if (progress !== null && this.elements.progressFill) {
            const validProgress = Math.max(0, Math.min(100, progress));
            this.elements.progressFill.style.width = `${validProgress}%`;
            this.elements.progressFill.style.backgroundColor = this._getProgressColor(validProgress);
        }
    }

    _getProgressColor(progress) {
        if (progress < 30) {
            return 'var(--primary-color)';
        } else if (progress < 70) {
            return 'var(--primary-hover)';
        } else {
            return 'var(--success-color)';
        }
    }

    setLoading(isLoading) {
        if (this.elements.progressBar) {
            this.elements.progressBar.style.display = isLoading ? 'block' : 'none';
        }
    }

    showError(message, duration = 3000) {
        this.updateStatus(message);
        if (this.elements.statusText) {
            this.elements.statusText.classList.add('error');
            setTimeout(() => {
                this.elements.statusText.classList.remove('error');
                this.updateStatus('准备就绪');
            }, duration);
        }
    }

    showSuccess(message, duration = 3000) {
        this.updateStatus(message);
        if (this.elements.statusText) {
            this.elements.statusText.classList.add('success');
            setTimeout(() => {
                this.elements.statusText.classList.remove('success');
                this.updateStatus('准备就绪');
            }, duration);
        }
    }

    setButtonState(buttonId, disabled) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = disabled;
            button.classList.toggle('disabled', disabled);
        }
    }

    _initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                const tooltip = element.getAttribute('data-tooltip');
                this._showTooltip(e.target, tooltip);
            });
            element.addEventListener('mouseleave', () => {
                this._hideTooltip();
            });
        });
    }

    _showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        const rect = element.getBoundingClientRect();
        tooltip.style.top = `${rect.bottom + 5}px`;
        tooltip.style.left = `${rect.left + (rect.width / 2)}px`;
        document.body.appendChild(tooltip);
    }

    _hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    _initializeThemeSupport() {
        const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        darkModeMediaQuery.addListener((e) => {
            this._updateTheme(e.matches);
        });
        this._updateTheme(darkModeMediaQuery.matches);
    }

    _updateTheme(isDark) {
        document.body.classList.toggle('dark-theme', isDark);
        document.body.classList.toggle('light-theme', !isDark);
    }

    addButtonClickEffect(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.addEventListener('click', () => {
                button.classList.add('clicked');
                setTimeout(() => {
                    button.classList.remove('clicked');
                }, 200);
            });
        }
    }

    updateSelectOptions(selectId, options) {
        const select = document.getElementById(selectId);
        if (select) {
            select.innerHTML = '';
            Object.entries(options).forEach(([value, label]) => {
                const option = document.createElement('option');
                option.value = value;
                option.textContent = label;
                select.appendChild(option);
            });
        }
    }
}

const uiUtils = new UIUtils();

/* ===== Photoshop工具 ===== */
class PSUtils {
    hasActiveDocument() {
        try {
            const { app } = require("photoshop");
            return !!app.activeDocument;
        } catch (error) {
            return false;
        }
    }

    getActiveDocument() {
        try {
            const { app } = require("photoshop");
            return app.activeDocument || null;
        } catch (error) {
            return null;
        }
    }

    getSelectedLayer() {
        if (!this.hasActiveDocument()) {
            return null;
        }

        try {
            const { app } = require("photoshop");
            const doc = app.activeDocument;
            return doc.activeLayers.length > 0 ? doc.activeLayers[0] : null;
        } catch (error) {
            return null;
        }
    }

    async getLayerInfo(layer) {
        if (!layer) {
            return null;
        }

        try {
            const { action } = require("photoshop");
            const { batchPlay } = action;

            const result = await batchPlay(
                [
                    {
                        _obj: "get",
                        _target: [
                            {
                                _ref: "layer",
                                _name: layer.name
                            }
                        ],
                        _options: {
                            dialogOptions: "dontDisplay"
                        }
                    }
                ],
                { synchronousExecution: true }
            );

            return result[0];
        } catch (error) {
            console.error("获取图层信息错误:", error);
            return null;
        }
    }

    async createTextLayer(doc, text, options) {
        if (!doc) {
            throw new Error("未提供文档对象");
        }

        try {
            const { action } = require("photoshop");
            const { batchPlay } = action;

            const result = await batchPlay(
                [
                    {
                        _obj: "make",
                        _target: [
                            {
                                _ref: "textLayer"
                            }
                        ],
                        using: {
                            _obj: "textLayer",
                            textKey: text,
                            textShape: [
                                {
                                    _obj: "textShape",
                                    bounds: {
                                        _obj: "bounds",
                                        left: options.x || 0,
                                        top: options.y || 0,
                                        right: (options.x || 0) + (options.width || 500),
                                        bottom: (options.y || 0) + (options.height || 100)
                                    },
                                    textType: {
                                        _enum: "textType",
                                        _value: "point"
                                    }
                                }
                            ],
                            textStyleRange: [
                                {
                                    _obj: "textStyleRange",
                                    from: 0,
                                    to: text.length,
                                    textStyle: {
                                        _obj: "textStyle",
                                        fontName: options.fontFamily || "ArialMT",
                                        fontStyleName: options.fontStyle || "Regular",
                                        size: {
                                            _unit: "pointsUnit",
                                            _value: options.fontSize || 24
                                        },
                                        color: {
                                            _obj: "RGBColor",
                                            red: options.color?.r || 0,
                                            green: options.color?.g || 0,
                                            blue: options.color?.b || 0
                                        }
                                    }
                                }
                            ]
                        },
                        _isCommand: true
                    }
                ],
                { synchronousExecution: true }
            );

            return result;
        } catch (error) {
            console.error("创建文字图层错误:", error);
            throw error;
        }
    }

    async getLayerPixels(layer) {
        if (!layer) {
            throw new Error("未提供图层对象");
        }

        try {
            const { action } = require("photoshop");
            const { batchPlay } = action;

            await batchPlay(
                [
                    {
                        _obj: "select",
                        _target: [
                            {
                                _ref: "layer",
                                _name: layer.name
                            }
                        ],
                        makeVisible: true,
                        _isCommand: true
                    }
                ],
                { synchronousExecution: true }
            );

            const layerInfo = await this.getLayerInfo(layer);
            const bounds = layerInfo.bounds;

            await batchPlay(
                [
                    {
                        _obj: "copy",
                        _isCommand: true
                    }
                ],
                { synchronousExecution: true }
            );

            return {
                bounds,
                hasPixelData: true
            };
        } catch (error) {
            console.error("获取图层像素数据错误:", error);
            throw error;
        }
    }

    async executeModal(callback, commandName = "PSUtils Operation") {
        try {
            const { core } = require("photoshop");
            const { executeAsModal } = core;

            return await executeAsModal(async () => {
                return await callback();
            }, { commandName });
        } catch (error) {
            console.error(`模态执行错误 (${commandName}):`, error);
            throw error;
        }
    }

    getDocumentResolution() {
        if (!this.hasActiveDocument()) {
            return 72;
        }

        try {
            const { app } = require("photoshop");
            return app.activeDocument.resolution;
        } catch (error) {
            return 72;
        }
    }

    getDocumentSize() {
        if (!this.hasActiveDocument()) {
            return { width: 0, height: 0 };
        }

        try {
            const { app } = require("photoshop");
            const doc = app.activeDocument;
            return {
                width: doc.width,
                height: doc.height
            };
        } catch (error) {
            return { width: 0, height: 0 };
        }
    }
}

const psUtils = new PSUtils();

/* ===== 图像处理工具 ===== */
class ImageUtils {
    /**
     * 将图层转换为Base64编码的图像数据
     * @param {Object} layer - Photoshop图层对象
     * @returns {Promise<string>} Base64编码的图像数据
     */
    async layerToBase64(layer) {
        try {
            const { action, app } = require("photoshop");
            const { batchPlay } = action;

            // 选择图层
            await batchPlay([
                {
                    _obj: "select",
                    _target: [{ _ref: "layer", _name: layer.name }],
                    makeVisible: true,
                    _isCommand: true
                }
            ], { synchronousExecution: true });

            // 复制图层内容
            await batchPlay([
                {
                    _obj: "copy",
                    _isCommand: true
                }
            ], { synchronousExecution: true });

            // 使用batchPlay创建新文档
            const docWidth = layer.bounds ? (layer.bounds.right - layer.bounds.left) : 100;
            const docHeight = layer.bounds ? (layer.bounds.bottom - layer.bounds.top) : 100;
            const docResolution = app.activeDocument ? app.activeDocument.resolution : 72;

            await batchPlay([
                {
                    _obj: "make",
                    _target: [{ _ref: "document" }],
                    new: {
                        _obj: "document",
                        artboard: false,
                        autoPromoteBackgroundLayer: false,
                        mode: { _enum: "newDocumentMode", _value: "RGBColor" },
                        width: { _unit: "pixelsUnit", _value: docWidth },
                        height: { _unit: "pixelsUnit", _value: docHeight },
                        resolution: { _unit: "densityUnit", _value: docResolution },
                        pixelScaleFactor: 1,
                        fill: { _enum: "fill", _value: "white" }
                    },
                    _isCommand: true
                }
            ], { synchronousExecution: true });

            // 粘贴到新文档
            await batchPlay([
                {
                    _obj: "paste",
                    _isCommand: true
                }
            ], { synchronousExecution: true });

            // 导出为Base64
            const base64Data = await this._exportDocumentAsBase64();

            // 关闭临时文档
            await batchPlay([
                {
                    _obj: "close",
                    _target: [{ _ref: "document", _enum: "ordinal", _value: "targetEnum" }],
                    saving: { _enum: "yesNo", _value: "no" },
                    _isCommand: true
                }
            ], { synchronousExecution: true });

            return base64Data;
        } catch (error) {
            logger.error("图层转Base64失败", error);
            throw new PluginError("无法将图层转换为图像数据", ErrorType.IMAGE_PROCESSING, error);
        }
    }

    /**
     * 将当前文档导出为Base64编码的JPEG数据
     * @returns {Promise<string>} Base64编码的JPEG数据
     */
    async _exportDocumentAsBase64() {
        try {
            const { action } = require("photoshop");
            const { batchPlay } = action;

            // 使用临时文件路径
            const tempPath = await this._getTempFilePath();

            // 导出为JPEG
            await batchPlay([
                {
                    _obj: "export",
                    _target: [{ _ref: "document", _enum: "ordinal", _value: "targetEnum" }],
                    as: {
                        _obj: "JPEG",
                        quality: 90,
                        matteColor: { _enum: "matteColor", _value: "none" }
                    },
                    in: { _path: tempPath, _kind: "local" },
                    _isCommand: true
                }
            ], { synchronousExecution: true });

            // 读取文件并转换为Base64
            const base64Data = await this._fileToBase64(tempPath);

            // 清理临时文件
            await this._deleteTempFile(tempPath);

            return base64Data;
        } catch (error) {
            logger.error("文档导出Base64失败", error);
            throw error;
        }
    }

    /**
     * 获取临时文件路径
     * @returns {Promise<string>} 临时文件路径
     */
    async _getTempFilePath() {
        const { storage } = require("uxp");
        const tempFolder = await storage.localFileSystem.getTemporaryFolder();
        const timestamp = Date.now();
        const fileName = `ps_ocr_temp_${timestamp}.jpg`;
        const tempFile = await tempFolder.createFile(fileName, { overwrite: true });
        return tempFile.nativePath;
    }

    /**
     * 将文件转换为Base64编码
     * @param {string} filePath - 文件路径
     * @returns {Promise<string>} Base64编码的文件内容
     */
    async _fileToBase64(filePath) {
        try {
            const { storage } = require("uxp");
            const file = await storage.localFileSystem.getFileForOpening(filePath);
            const arrayBuffer = await file.read({ format: storage.formats.binary });

            // 将ArrayBuffer转换为Base64
            const uint8Array = new Uint8Array(arrayBuffer);
            let binaryString = '';
            for (let i = 0; i < uint8Array.length; i++) {
                binaryString += String.fromCharCode(uint8Array[i]);
            }

            return btoa(binaryString);
        } catch (error) {
            logger.error("文件转Base64失败", error);
            throw error;
        }
    }

    /**
     * 删除临时文件
     * @param {string} filePath - 文件路径
     */
    async _deleteTempFile(filePath) {
        try {
            const { storage } = require("uxp");
            const file = await storage.localFileSystem.getFileForOpening(filePath);
            await file.delete();
        } catch (error) {
            logger.warn("删除临时文件失败", error);
        }
    }

    /**
     * 验证图像数据
     * @param {string} base64Data - Base64编码的图像数据
     * @returns {boolean} 是否为有效的图像数据
     */
    validateImageData(base64Data) {
        if (!base64Data || typeof base64Data !== 'string') {
            return false;
        }

        // 检查Base64格式
        const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
        return base64Regex.test(base64Data) && base64Data.length > 100;
    }

    /**
     * 计算图像的推荐压缩质量
     * @param {number} fileSize - 文件大小（字节）
     * @returns {number} 推荐的压缩质量（1-100）
     */
    calculateOptimalQuality(fileSize) {
        // 百度OCR API建议图像大小不超过4MB
        const maxSize = 4 * 1024 * 1024; // 4MB

        if (fileSize <= maxSize) {
            return 90; // 高质量
        } else if (fileSize <= maxSize * 2) {
            return 75; // 中等质量
        } else {
            return 60; // 较低质量，确保文件大小合适
        }
    }
}

const imageUtils = new ImageUtils();

// 导出所有工具
window.logger = logger;
window.errorHandler = errorHandler;
window.uiUtils = uiUtils;
window.psUtils = psUtils;
window.imageUtils = imageUtils;
window.ErrorType = ErrorType;
window.PluginError = PluginError;
