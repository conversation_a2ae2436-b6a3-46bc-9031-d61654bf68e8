/* 自定义样式 - 美化UI */

/* 隐藏批量处理相关的样式 */
.batch-options,
html.compact-mode .batch-options {
    display: none !important;
}

/* 调整整体行距和间距 */
.section {
    padding: 4px 8px;
    margin-bottom: 4px;
}

.section-header {
    margin-bottom: 6px;
}

.section h3 {
    font-size: 14px;
    line-height: 1.1;
    margin: 0;
}

/* 美化特性区块 */
.feature-section {
    position: relative;
    overflow: hidden;
    background: linear-gradient(145deg, rgba(60, 60, 60, 0.7), rgba(45, 45, 45, 0.7)) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.feature-section::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, rgba(38, 128, 235, 0.05) 0%, rgba(38, 128, 235, 0) 70%);
    z-index: -1;
    border-radius: 12px;
    filter: blur(8px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
}

.feature-section:hover::before {
    opacity: 1;
    background: linear-gradient(45deg, rgba(38, 128, 235, 0.1) 0%, rgba(38, 128, 235, 0) 70%);
}

/* 美化按钮 */
.sp-button {
    position: relative;
    overflow: hidden;
    border-radius: 6px;
    transition: all 0.3s ease;
    line-height: 1.1;
}

.sp-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sp-button:hover::after {
    opacity: 1;
}

.sp-button.primary {
    background: linear-gradient(135deg, #2680EB, #1473E6);
    box-shadow: 0 2px 8px rgba(38, 128, 235, 0.3);
    border: none;
}

.sp-button.primary:hover {
    background: linear-gradient(135deg, #1473E6, #0d66d0);
    box-shadow: 0 4px 12px rgba(38, 128, 235, 0.5);
    transform: translateY(-1px);
}

.sp-button.secondary {
    background: linear-gradient(135deg, rgba(70, 70, 70, 0.8), rgba(55, 55, 55, 0.8));
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.sp-button.secondary:hover {
    background: linear-gradient(135deg, rgba(80, 80, 80, 0.8), rgba(65, 65, 65, 0.8));
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transform: translateY(-1px);
}

/* 美化标题 */
.section-header h3 {
    position: relative;
    padding-left: 14px;
    font-size: 14px;
    font-weight: 600;
}

.section-header h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(38, 128, 235, 0.6);
}

/* 美化头部 */
.header-section {
    background: linear-gradient(145deg, rgba(50, 50, 50, 0.8), rgba(40, 40, 40, 0.8));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 8px;
    text-align: center;
    padding: 8px;
    margin-bottom: 8px;
    position: relative;
    overflow: hidden;
}

.header-section h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 2px;
    background: linear-gradient(135deg, #ffffff, #b8b8b8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-section .description {
    font-size: 13px;
    color: #b8b8b8;
}

.header-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, 
        rgba(255, 255, 255, 0), 
        rgba(255, 255, 255, 0.1), 
        rgba(255, 255, 255, 0));
}

/* 美化底部 */
.footer {
    background: linear-gradient(145deg, rgba(50, 50, 50, 0.8), rgba(40, 40, 40, 0.8));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 6px;
    margin-top: 8px;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, 
        rgba(255, 255, 255, 0), 
        rgba(255, 255, 255, 0.1), 
        rgba(255, 255, 255, 0));
}

.version {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    padding: 2px 6px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    font-size: 11px;
    color: #888888;
}

.version:hover {
    background-color: rgba(38, 128, 235, 0.2);
    color: #ffffff;
}

/* 美化下拉菜单 */
.sp-dropdown {
    padding: 4px 24px 4px 8px;
    border-radius: 6px;
    background-color: rgba(60, 60, 60, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="%23E6E6E6" d="M6 6L0 0h12z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 8px center;
}

.sp-dropdown:hover {
    background-color: rgba(70, 70, 70, 0.8);
    border-color: rgba(38, 128, 235, 0.5);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 0 4px rgba(38, 128, 235, 0.4);
}

/* 美化滑块 */
.slider {
    height: 3px;
    border-radius: 2px;
    background: linear-gradient(to right, rgba(38, 128, 235, 0.3), rgba(38, 128, 235, 0.1));
}

.slider::-webkit-slider-thumb {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2680EB, #1473E6);
    box-shadow: 0 0 8px rgba(38, 128, 235, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    background: linear-gradient(135deg, #1473E6, #0d66d0);
}

/* 美化容器 */
.container {
    background: linear-gradient(180deg, rgba(50, 50, 50, 1) 0%, rgba(40, 40, 40, 1) 100%);
    padding: 8px 8px 8px 8px;
}

/* 美化高级选项 */
.advanced-options {
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 6px;
    padding: 8px;
    margin-top: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    animation: fadeIn 0.3s ease;
}

/* 美化复选框 */
.checkbox-label {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    background-color: rgba(60, 60, 60, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    margin-right: 6px;
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked {
    background: linear-gradient(135deg, #2680EB, #1473E6);
    border-color: transparent;
}

.checkbox-label:hover input[type="checkbox"]:not(:checked) {
    background-color: rgba(70, 70, 70, 0.8);
    border-color: rgba(38, 128, 235, 0.5);
}

/* 美化状态栏 */
.status-section {
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 6px;
    padding: 6px;
    margin-top: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.status-text {
    font-size: 12px;
    color: #b8b8b8;
    text-align: center;
    margin: 0;
}

.status-text.success {
    color: var(--success-color);
}

.status-text.error {
    color: var(--error-color);
}

/* 美化进度条 */
.progress-bar {
    height: 3px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 6px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(to right, #2680EB, #1473E6);
    width: 0%;
    transition: width 0.3s ease;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 修复间距 */
.action-buttons {
    margin-top: 8px;
}

.language-options {
    margin-top: 8px;
}

/* 优化文字识别部分排版 */
.section.feature-section:first-child {
    margin-top: 8px;
    border-top: 2px solid rgba(38, 128, 235, 0.4);
}

.language-options {
    display: flex;
    align-items: center;
    background-color: rgba(40, 40, 40, 0.6);
    border-radius: 6px;
    padding: 6px 8px;
    margin-top: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.language-options label {
    font-weight: 500;
    color: #b8b8b8;
    margin-right: 8px;
    font-size: 12px;
}

.action-buttons {
    display: flex;
    justify-content: center;
    margin-top: 8px;
}

.action-buttons .sp-button {
    min-width: 120px;
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 600;
}

.action-buttons .sp-button .button-icon {
    font-size: 14px;
    margin-right: 6px;
}

/* 美化文字识别按钮 */
#extractText {
    background: linear-gradient(135deg, #2680EB, #1473E6);
    box-shadow: 0 3px 10px rgba(38, 128, 235, 0.4);
    border: none;
    transition: all 0.3s ease;
    transform-origin: center;
}

#extractText:hover {
    background: linear-gradient(135deg, #1473E6, #0d66d0);
    box-shadow: 0 5px 15px rgba(38, 128, 235, 0.6);
    transform: translateY(-2px) scale(1.02);
}

#extractText:active {
    transform: translateY(1px) scale(0.98);
    box-shadow: 0 2px 8px rgba(38, 128, 235, 0.3);
}

/* 调整图像处理按钮样式 */
.button-group {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    margin-top: 8px;
}

.button-group .sp-button {
    flex: 1;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: 500;
}

/* 添加首次加载动画 */
.main-content {
    animation: slideInUp 0.5s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 优化容器顶部间距 */
.container {
    padding-top: 8px;
}

/* 优化主内容区域滚动 */
.main-content {
    padding-right: 0;
    margin-right: 0;
    gap: 6px;
}

/* 调整滚动条位置，使其靠边 */
::-webkit-scrollbar {
    width: 3px;
    height: 3px;
    position: absolute;
    right: 0;
}

::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 0;
    margin: 0;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 0;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.25);
}

/* 调整行距和元素间距 */
.option-group {
    margin-bottom: 4px;
}

.option-group label {
    margin-bottom: 2px;
    font-size: 12px;
}

/* 调整对话框内部间距 */
.dialog-header {
    padding: 6px 8px;
}

.dialog-body {
    padding: 8px;
}

.dialog-footer {
    padding: 6px 8px;
}

/* 整体行高调整 */
body {
    line-height: 1.2;
}

/* 紧凑模式样式 */
html.compact-mode body {
    font-size: 12px;
}

html.compact-mode .container {
    padding: 4px;
    gap: 4px;
    min-height: 350px;
}

html.compact-mode .section {
    padding: 4px 6px;
    margin-bottom: 4px;
}

html.compact-mode .section-header {
    margin-bottom: 4px;
}

html.compact-mode .section h3 {
    font-size: 13px;
}

html.compact-mode .language-options {
    padding: 4px 6px;
    margin-top: 6px;
}

html.compact-mode .language-options label {
    font-size: 11px;
}

html.compact-mode .button-group {
    gap: 4px;
    margin-top: 6px;
}

html.compact-mode .button-group .sp-button {
    padding: 4px 8px;
    min-height: 26px;
    font-size: 11px;
}

html.compact-mode .action-buttons {
    margin-top: 6px;
}

html.compact-mode .action-buttons .sp-button {
    min-width: 100px;
    padding: 4px 10px;
    font-size: 11px;
}

html.compact-mode .advanced-options {
    padding: 4px 6px;
    margin-top: 4px;
}

html.compact-mode .option-group {
    margin-bottom: 4px;
}

html.compact-mode .checkbox-label {
    margin-bottom: 3px;
    font-size: 11px;
}

html.compact-mode .status-section {
    padding: 4px;
    margin-top: 4px;
}

html.compact-mode .footer {
    padding: 4px;
    margin-top: 4px;
}

html.compact-mode .section h3,
html.compact-mode .button-group .sp-button,
html.compact-mode .action-buttons .sp-button {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

html.compact-mode .main-content {
    overflow-y: auto;
    max-height: calc(100vh - 100px);
    padding-right: 2px;
}

html.compact-mode ::-webkit-scrollbar {
    width: 2px;
}

html.compact-mode ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.15);
}

html.compact-mode .tooltip .tooltip-text {
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
}

html.compact-mode .button-icon {
    margin-right: 3px;
    font-size: 11px;
} 
