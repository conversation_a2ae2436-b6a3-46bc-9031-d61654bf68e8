<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PS图片转</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="main-content">
            <div class="section feature-section">
                <div class="section-header">
                    <h3>文字识别</h3>
                    <div class="tooltip">
                        <span class="help-icon">?</span>
                        <span class="tooltip-text">选择图层后点击按钮，自动识别图像中的文字</span>
                    </div>
                </div>
                <div class="language-options">
                    <label for="languageSelect">识别语言:</label>
                    <select id="languageSelect" class="sp-dropdown">
                        <option value="auto">自动检测</option>
                        <option value="zh">中文</option>
                        <option value="en">英文</option>
                        <option value="ja">日文</option>
                        <option value="ko">韩文</option>
                        <option value="ru">俄文</option>
                    </select>
                </div>
                <div class="action-buttons">
                    <button id="extractText" class="sp-button primary">
                        <span class="button-icon">📝</span>提取文字
                    </button>
                </div>
            </div>

            <div class="section feature-section">
                <div class="section-header">
                    <h3>图像处理</h3>
                    <div class="tooltip">
                        <span class="help-icon">?</span>
                        <span class="tooltip-text">选择图层后点击按钮，自动处理图像</span>
                    </div>
                </div>
                <div class="button-group">
                    <button id="makeTransparent" class="sp-button secondary">
                        <span class="button-icon">🎯</span>背景透明
                    </button>
                    <button id="separateElements" class="sp-button secondary">
                        <span class="button-icon">✂️</span>元素分离
                    </button>
                </div>
            </div>

            <div class="section" id="advancedSection">
                <div class="section-header">
                    <h3>高级选项</h3>
                    <button id="toggleAdvanced" class="sp-button quiet small">展开</button>
                </div>
                <div class="advanced-options" style="display: none;">
                    <div class="option-group">
                        <label for="smoothnessSlider">边缘平滑度:</label>
                        <div class="slider-container">
                            <input type="range" id="smoothnessSlider" min="0" max="100" value="5" class="slider">
                            <span id="smoothnessValue" class="slider-value">5</span>
                        </div>
                    </div>
                    <div class="option-group">
                        <label for="featherSlider">羽化半径:</label>
                        <div class="slider-container">
                            <input type="range" id="featherSlider" min="0" max="10" value="1" step="0.1" class="slider">
                            <span id="featherValue" class="slider-value">1.0</span>
                        </div>
                    </div>
                    <div class="option-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="preserveOriginal" checked>
                            <span>保留原始图层</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="status-section">
            <div id="progressBar" class="progress-bar" style="display: none;">
                <div class="progress-fill"></div>
            </div>
            <p id="statusText" class="status-text">准备就绪</p>
        </div>

        <div class="section footer">
            <div class="flex justify-between align-center">
                <button id="showSettings" class="sp-button quiet small">
                    <span class="button-icon">⚙️</span>设置
                </button>
                <span class="version">v1.0.3</span>
            </div>
        </div>
    </div>

    <div id="settingsDialog" class="dialog" style="display: none;">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>设置</h3>
                <button id="closeSettings" class="close-button">&times;</button>
            </div>
            <div class="dialog-body">
                <div class="settings-section">
                    <h4>OCR设置</h4>
                    <div class="option-group">
                        <label for="accuracySlider">识别精度:</label>
                        <div class="slider-container">
                            <input type="range" id="accuracySlider" min="0" max="100" value="90" class="slider">
                            <span id="accuracyValue" class="slider-value">90</span>
                        </div>
                    </div>
                    <div class="option-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableMultiLanguage" checked>
                            <span>启用多语言检测</span>
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>性能设置</h4>
                    <div class="option-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="downsampleLargeImages" checked>
                            <span>大图像自动降采样</span>
                        </label>
                    </div>
                    <div class="option-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableMultiThreading" checked>
                            <span>启用多线程处理</span>
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>界面设置</h4>
                    <div class="option-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="adaptToSystemTheme" checked>
                            <span>自动适应系统主题</span>
                        </label>
                    </div>
                    <div class="option-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showAdvancedOptions">
                            <span>默认显示高级选项</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="dialog-footer">
                <button id="resetSettings" class="sp-button secondary">重置</button>
                <button id="saveSettings" class="sp-button primary">保存</button>
            </div>
        </div>
    </div>

    <script src="utils.js"></script>
    <script src="services.js"></script>
    <script src="main.js"></script>
</body>
</html>