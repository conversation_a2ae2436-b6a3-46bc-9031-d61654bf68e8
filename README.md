# PS图片转插件

一个功能强大的Photoshop UXP插件，提供图像处理和文字识别功能。

## ✨ 主要功能

### 🔤 文字识别 (OCR) - 集成百度OCR API
- **高精度识别**：集成百度OCR API，支持任意图像的文字识别
- **多语言支持**：中文、英文、日文、韩文、俄文及自动检测
- **智能分析**：自动检测文字区域、类型和属性
- **文字重建**：创建可编辑的文本图层，保持原始位置和样式
- **无硬编码**：真实OCR识别，不依赖预设数据
- **智能去除**：可选择性去除原始文字，保持背景完整

### 🎨 背景透明化
- **一键移除**：智能识别主体，自动移除背景
- **边缘优化**：自动羽化和平滑处理
- **非破坏性**：使用图层蒙版，保留原始图像

### 🧩 元素分离
- **智能分离**：自动分离图像中的不同元素
- **独立图层**：为每个元素创建独立图层
- **保持质量**：无损分离，保持原始图像质量

## 📋 系统要求
- **Photoshop版本**：2022 (23.0) 或更高版本
- **UXP Runtime**：1.12.0 或更高版本
- **操作系统**：Windows 10+ / macOS 10.15+

## 🚀 安装方法

### 方法一：通过Photoshop插件管理器
1. 下载插件包 (.ccx 文件)
2. 打开Photoshop，选择 **插件** > **管理插件**
3. 点击 **安装**，选择下载的插件包
4. 重启Photoshop完成安装

### 方法二：手动安装
1. 将插件文件夹复制到Photoshop插件目录：
   - **Windows**: `C:\Program Files\Adobe\Adobe Photoshop 2025\Plug-ins\`
   - **macOS**: `/Applications/Adobe Photoshop 2025/Plug-ins/`
2. 重启Photoshop

## 📖 使用指南

### 基本操作
1. 打开Photoshop，选择 **窗口** > **扩展功能** > **PS图片转**
2. 在图层面板中选择要处理的图层
3. 在插件面板中选择相应功能
4. 点击执行按钮开始处理

### 文字识别使用技巧
- **选择合适的语言**：提高识别准确率，支持自动检测
- **图像质量**：确保文字清晰可见，对比度高
- **图层选择**：选择包含文字的图层
- **网络连接**：需要互联网连接访问百度OCR API
- **API配置**：插件已预配置API密钥，无需额外设置

### 背景透明化技巧
- **主体清晰**：确保主体与背景有明显对比
- **边缘处理**：可调整羽化参数优化边缘
- **复杂背景**：可能需要手动调整蒙版

## ❓ 常见问题

### 窗口大小问题
- **问题**：插件界面显示不完整
- **解决**：插件支持自适应窗口大小，会自动进入紧凑模式
- **建议**：最小窗口尺寸 300×400px，推荐 400×600px

### "系统不支持matchMedia"错误
- **问题**：UXP环境中matchMedia API不可用
- **状态**：已在1.0.3版本修复
- **解决**：如仍出现，请重启Photoshop

### 文字识别不准确
- **检查图像质量**：确保文字清晰，对比度足够
- **选择正确语言**：在语言下拉菜单中选择对应语言
- **图层内容**：确保选中的图层包含文字内容
- **网络连接**：检查网络连接，确保可以访问百度OCR API
- **API限制**：百度OCR API有调用频率限制，请适度使用

### 背景透明化效果不理想
- **主体选择**：确保主体与背景有明显区别
- **图像复杂度**：复杂背景可能需要手动调整
- **后期处理**：可以手动编辑生成的蒙版

## 🏗️ 项目结构

```
ps图片转/
├── 📄 index.html          # 主界面文件
├── 🎨 styles.css          # 合并的样式文件
├── ⚙️ main.js             # 主逻辑文件
├── 🔧 utils.js            # 工具类合集
├── 🛠️ services.js         # 服务类合集（包含百度OCR集成）
├── 🧪 test-baidu-ocr.js   # 百度OCR测试文件
├── 📋 manifest.json       # UXP插件配置
├── 📦 package.json        # 项目配置
├── 📖 README.md           # 说明文档
└── 🖼️ icons/              # 图标资源
    ├── icon-dark.svg
    ├── icon-light.svg
    ├── icon-23.png
    └── plugin-icon.svg
```

### 文件说明
- **index.html** - 插件主界面，定义UI结构
- **styles.css** - 统一样式文件，包含所有CSS样式
- **main.js** - 核心逻辑，处理用户交互和功能调用
- **utils.js** - 工具类合集（日志、错误处理、UI工具、PS工具）
- **services.js** - 服务类合集（百度OCR服务、图像处理服务）
- **test-baidu-ocr.js** - 百度OCR API测试文件
- **manifest.json** - UXP插件配置文件
- **package.json** - 项目依赖和脚本配置

## 📝 更新日志

### 版本 1.0.3 (最新) - 2024.12
#### 🔧 文件结构优化
- **CSS文件合并**：将3个CSS文件合并为1个，减少67%的文件数量
- **JavaScript模块整合**：合并utils和services目录下的文件
- **项目精简**：删除冗余文件和空目录，优化项目结构

#### 🎨 界面优化
- **样式统一**：重新组织CSS变量和样式规范
- **视觉增强**：添加渐变效果、动画和交互反馈
- **紧凑模式**：优化间距和尺寸，界面更紧凑美观
- **响应式设计**：改进自适应窗口大小处理

#### 🐛 问题修复
- **UXP兼容性**：修复matchMedia API不支持问题
- **文件引用**：更新HTML中的文件引用路径
- **版本同步**：统一manifest.json和package.json版本号

#### ⚡ 性能提升
- **加载速度**：减少HTTP请求数量，提升加载性能
- **代码优化**：精简和重构代码，提高执行效率
- **内存使用**：优化资源管理，减少内存占用

### 版本 1.0.2
#### 🌐 多语言支持
- 添加文字识别多语言支持（中、英、日、韩、俄）
- 优化OCR识别准确率
- 改进语言选择界面

#### 🎨 UI界面优化
- 重新设计插件界面
- 添加进度条和状态提示
- 优化按钮和控件样式

### 版本 1.0.1
#### 🚀 初始功能
- 实现基础文字识别功能
- 添加背景透明化处理
- 实现元素分离功能
- 建立基础UI框架

## 🛠️ 开发相关

### 开发环境设置
```bash
# 安装依赖
npm install

# 启动开发服务器
npm start

# 监听文件变化
npm run watch

# 构建插件包
npm run build

# 代码检查
npm run lint

# 代码格式化
npm run format
```

### 技术栈
- **前端框架**：原生HTML5 + CSS3 + JavaScript ES6+
- **插件框架**：Adobe UXP (User Experience Platform)
- **开发工具**：ESLint + Prettier
- **构建工具**：UXP Developer Tool

### 贡献指南
1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 支持与反馈

- **问题报告**：[GitHub Issues](https://github.com/example/ps-image-converter/issues)
- **功能建议**：[GitHub Discussions](https://github.com/example/ps-image-converter/discussions)
- **邮件联系**：<EMAIL>

---

**感谢使用 PS图片转插件！** 🎉