{"name": "ps-image-converter", "version": "1.0.3", "description": "PS图片转 - 图像处理与文字识别工具", "main": "main.js", "scripts": {"start": "uxp plugin serve", "watch": "uxp plugin watch", "build": "uxp plugin package", "debug": "uxp plugin debug", "lint": "eslint .", "format": "prettier --write .", "clean": "<PERSON><PERSON><PERSON> dist build", "prebuild": "npm run clean"}, "keywords": ["photoshop", "uxp", "plugin", "image", "ocr", "text", "transparent", "background"], "author": "Example Developer", "license": "MIT", "dependencies": {}, "devDependencies": {"eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.0", "prettier": "^2.8.0", "rimraf": "^5.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/example/ps-image-converter.git"}, "bugs": {"url": "https://github.com/example/ps-image-converter/issues"}, "homepage": "https://github.com/example/ps-image-converter#readme", "prettier": {"singleQuote": true, "trailingComma": "es5", "tabWidth": 4, "semi": true, "printWidth": 100}, "eslintConfig": {"extends": ["prettier"], "plugins": ["prettier"], "env": {"browser": true, "node": true, "es6": true}, "rules": {"prettier/prettier": "error"}}}