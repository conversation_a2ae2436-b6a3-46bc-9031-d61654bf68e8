// PS图片转插件 - 主要JavaScript文件

document.addEventListener('DOMContentLoaded', function() {
    // 插件配置
    const CONFIG = {
        // 插件版本
        VERSION: '1.0.3',

        // API版本
        API_VERSION: 2,

        // 最小UXP运行时版本
        MIN_UXP_VERSION: '1.12.0',

        // 默认设置
        DEFAULT_SETTINGS: {
            accuracy: 90,
            enableMultiLanguage: true,
            downsampleLargeImages: true,
            enableMultiThreading: true,
            adaptToSystemTheme: true,
            showAdvancedOptions: false,
            smoothness: 5,
            feather: 1.0,
            preserveOriginal: true
        },

        // 支持的语言
        SUPPORTED_LANGUAGES: [
            { code: 'auto', name: '自动检测' },
            { code: 'zh', name: '中文' },
            { code: 'en', name: '英文' },
            { code: 'ja', name: '日文' },
            { code: 'ko', name: '韩文' },
            { code: 'ru', name: '俄文' }
        ],

        // UI配置
        UI: {
            MIN_PANEL_WIDTH: 260,
            MIN_PANEL_HEIGHT: 450,
            COMPACT_HEIGHT: 500,
            ANIMATION_DURATION: 300
        }
    };

    // 兼容性工具 - 处理UXP环境中不支持的API
    function initCompatibility() {
        // 模拟matchMedia API
        if (typeof window.matchMedia !== 'function') {
            window.matchMedia = function(query) {
                // 在UXP环境中，默认使用深色主题
                const matches = query.includes('prefers-color-scheme: dark');

                return {
                    matches: matches,
                    media: query,
                    addEventListener: function() {},
                    removeEventListener: function() {},
                    dispatchEvent: function() { return true; }
                };
            };

            console.log('已添加matchMedia兼容性支持');
        }

        // 模拟animate API
        if (typeof Element.prototype.animate !== 'function') {
            Element.prototype.animate = function(keyframes, options) {
                // 简单的动画模拟，仅应用最终状态
                const lastKeyframe = keyframes[keyframes.length - 1];
                const element = this;

                // 应用最终样式
                if (typeof lastKeyframe === 'object') {
                    Object.keys(lastKeyframe).forEach(property => {
                        element.style[property] = lastKeyframe[property];
                    });
                }

                return {
                    finished: Promise.resolve(),
                    cancel: function() {}
                };
            };

            console.log('已添加animate兼容性支持');
        }

        console.log('兼容性工具已初始化');
    }

    // 初始化兼容性工具
    initCompatibility();

    // 获取UI元素
    const extractTextBtn = document.getElementById('extractText');
    const makeTransparentBtn = document.getElementById('makeTransparent');
    const separateElementsBtn = document.getElementById('separateElements');
    const showSettingsBtn = document.getElementById('showSettings');
    const toggleAdvancedBtn = document.getElementById('toggleAdvanced');
    const languageSelect = document.getElementById('languageSelect');
    const advancedSection = document.getElementById('advancedSection');

    // 设置对话框元素
    const settingsDialog = document.getElementById('settingsDialog');
    const closeSettingsBtn = document.getElementById('closeSettings');
    const saveSettingsBtn = document.getElementById('saveSettings');
    const resetSettingsBtn = document.getElementById('resetSettings');

    // 进度条元素
    const progressBar = document.getElementById('progressBar');
    const progressFill = progressBar.querySelector('.progress-fill');
    const statusText = document.getElementById('statusText');

    // 滑块元素
    const sliders = {
        smoothness: document.getElementById('smoothnessSlider'),
        feather: document.getElementById('featherSlider'),
        accuracy: document.getElementById('accuracySlider')
    };

    const sliderValues = {
        smoothness: document.getElementById('smoothnessValue'),
        feather: document.getElementById('featherValue'),
        accuracy: document.getElementById('accuracyValue')
    };

    // 默认设置
    const defaultSettings = CONFIG.DEFAULT_SETTINGS;

    // 当前主题状态
    let isDarkTheme = true;

    // 初始化插件
    async function initPlugin() {
        try {
            // 检查Photoshop是否可用
            await checkPhotoshopAvailability();
            console.log('Photoshop已连接');

            // 注册事件监听器
            registerEventListeners();

            // 初始化滑块值
            updateAllSliderValues();

            // 加载保存的设置
            loadSettings();

            // 检测系统主题
            detectSystemTheme();

            // 添加入场动画
            addEntryAnimation();

            // 添加窗口调整大小处理
            addResizeHandling();

            // 更新状态
            showStatus('准备就绪，请选择操作');
        } catch (error) {
            console.error('初始化插件失败:', error);
            showError('无法连接到Photoshop，请确保Photoshop已启动。');
        }
    }

    // 添加入场动画
    function addEntryAnimation() {
        const sections = document.querySelectorAll('.section');

        // 使用setTimeout模拟动画，兼容UXP环境
        sections.forEach((section, index) => {
            // 初始状态
            section.style.opacity = '0';
            section.style.transform = 'translateY(20px)';
            section.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

            // 延迟显示
            setTimeout(() => {
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            }, 100 + index * 100);
        });
    }

    // 检测系统主题
    function detectSystemTheme() {
        // 默认使用深色主题
        isDarkTheme = true;

        try {
            // 使用我们的兼容性工具后，matchMedia应该总是可用
            const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
            isDarkTheme = prefersDarkMode;

            // 监听系统主题变化
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (getSetting('adaptToSystemTheme')) {
                    isDarkTheme = e.matches;
                    applyTheme();
                }
            });
        } catch (error) {
            console.log('使用默认深色主题');
        }

        // 应用主题
        applyTheme();
    }

    // 应用主题
    function applyTheme() {
        const root = document.documentElement;
        if (isDarkTheme) {
            root.style.setProperty('--background-color', '#2D2D2D');
            root.style.setProperty('--panel-color', '#323232');
            root.style.setProperty('--text-color', '#E6E6E6');
            root.style.setProperty('--border-color', '#464646');
        } else {
            root.style.setProperty('--background-color', '#E8E8E8');
            root.style.setProperty('--panel-color', '#F5F5F5');
            root.style.setProperty('--text-color', '#232323');
            root.style.setProperty('--border-color', '#D0D0D0');
        }
    }

    // 检查Photoshop是否可用
    async function checkPhotoshopAvailability() {
        try {
            // 使用UXP API检查Photoshop是否可用
            const app = require('photoshop').app;

            // 获取Photoshop版本信息
            let appVersion = "未知";
            try {
                appVersion = app.version || "未知";
            } catch (err) {
                console.error('无法获取Photoshop版本:', err);
            }

            console.log(`Photoshop版本: ${appVersion}`);
            return true;
        } catch (error) {
            console.error('Photoshop不可用:', error);
            throw new Error('Photoshop不可用');
        }
    }

    // 注册事件监听器
    function registerEventListeners() {
        // 主要功能按钮
        extractTextBtn?.addEventListener('click', handleExtractText);
        makeTransparentBtn?.addEventListener('click', handleMakeTransparent);
        separateElementsBtn?.addEventListener('click', handleSeparateElements);

        // 设置对话框
        showSettingsBtn?.addEventListener('click', handleShowSettings);
        closeSettingsBtn?.addEventListener('click', handleCloseSettings);
        saveSettingsBtn?.addEventListener('click', handleSaveSettings);
        resetSettingsBtn?.addEventListener('click', handleResetSettings);

        // 高级选项
        toggleAdvancedBtn?.addEventListener('click', handleToggleAdvanced);

        // 滑块事件
        Object.keys(sliders).forEach(key => {
            const slider = sliders[key];
            if (slider) {
                slider.addEventListener('input', () => updateSliderValue(key));
            }
        });

        // 点击对话框外部关闭
        settingsDialog?.addEventListener('click', (e) => {
            if (e.target === settingsDialog) {
                handleCloseSettings();
            }
        });

        // 按键事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && settingsDialog.style.display === 'flex') {
                handleCloseSettings();
            }
        });
    }

    // 处理文字识别
    async function handleExtractText() {
        try {
            addButtonLoadingState(extractTextBtn);
            showProgress('正在识别文字...', 0);

            // 检查语言选择器是否存在
            if (!languageSelect) {
                console.error('语言选择器未找到');
                showError('界面初始化错误，请刷新插件');
                removeButtonLoadingState(extractTextBtn);
                hideProgress();
                return;
            }

            const language = languageSelect.value || 'auto';
            console.log(`使用语言: ${language}`);

            // 获取Photoshop API
            const { app, action } = require('photoshop');
            const { batchPlay } = action;

            // 检查是否有活动文档
            if (!app.documents || app.documents.length === 0) {
                showError('没有打开的文档，请先打开一个文档');
                removeButtonLoadingState(extractTextBtn);
                hideProgress();
                return;
            }

            // 获取当前文档和选中的图层
            const doc = app.activeDocument;
            const selectedLayers = doc.activeLayers;

            if (!selectedLayers || selectedLayers.length === 0) {
                showError('没有选中图层，请先选择一个图层');
                removeButtonLoadingState(extractTextBtn);
                hideProgress();
                return;
            }

            const targetLayer = selectedLayers[0];

            // 使用全局OCR服务
            // ocrService已通过services.js加载到全局

            // 获取图层边界和属性
            let bounds, layerInfo;
            try {
                // 使用batchPlay获取图层边界和详细信息
                const result = await batchPlay(
                    [
                        {
                            _obj: "get",
                            _target: [
                                {
                                    _ref: "layer",
                                    _enum: "ordinal",
                                    _value: "targetEnum"
                                }
                            ],
                            _options: {
                                dialogOptions: "dontDisplay"
                            }
                        }
                    ],
                    {
                        synchronousExecution: true
                    }
                );

                if (result && result[0]) {
                    layerInfo = result[0];
                    bounds = layerInfo.bounds;

                    // 如果是智能对象，尝试获取其内容的边界
                    if (layerInfo.smartObject) {
                        console.log("检测到智能对象图层");
                    }

                    console.log("图层信息:", JSON.stringify(bounds));
                } else {
                    // 如果无法获取边界，使用默认值
                    bounds = {
                        left: 0,
                        top: 0,
                        right: doc.width,
                        bottom: doc.height
                    };
                }
            } catch (error) {
                console.error('获取图层信息错误:', error);
                bounds = {
                    left: 0,
                    top: 0,
                    right: doc.width,
                    bottom: doc.height
                };
            }

            // 准备图像数据
            const imageData = {
                bounds,
                hasPixelData: true,
                layerInfo
            };

            // 执行高级OCR识别（包含文字去除）
            showProgress('正在高精度分析图像中的文字...', 20);
            const recognitionResult = await ocrService.recognizeText(
                imageData,
                language,
                (message, progress) => {
                    showProgress(message, progress);
                },
                true // 启用原始文字去除
            );

            const recognitionResults = recognitionResult.textElements;

            // 调试信息
            console.log('OCR识别结果:', recognitionResult);
            console.log('文字元素数量:', recognitionResults.length);
            recognitionResults.forEach((result, index) => {
                console.log(`文字 ${index + 1}:`, {
                    text: result.text,
                    bounds: result.bounds,
                    confidence: result.confidence,
                    type: result.type
                });
            });

            // 创建文字图层
            showProgress('正在创建文字图层...', 90);

            // 创建一个图层组来存放所有文字图层
            const layerGroupName = `${targetLayer.name || '图层'} - 文字图层`;

            try {
                // 创建图层组
                await batchPlay(
                    [
                        {
                            _obj: "make",
                            _target: [
                                {
                                    _ref: "layerSection"
                                }
                            ],
                            layerSectionStart: 0,
                            layerSectionEnd: 0,
                            name: layerGroupName,
                            _isCommand: true
                        }
                    ],
                    { synchronousExecution: true }
                );

                // 为每个识别结果创建文字图层
                for (let i = 0; i < recognitionResults.length; i++) {
                    const result = recognitionResults[i];

                    // 跳过空文字或置信度过低的结果
                    if (!result.text || result.text.trim() === '' || (result.confidence && result.confidence < 0.5)) {
                        continue;
                    }

                    // 创建文字图层名称
                    const textLayerName = result.type ?
                        `${result.text.substring(0, 15)}${result.text.length > 15 ? '...' : ''} (${result.type})` :
                        `文字 ${i+1}`;

                    // 使用精确位置信息
                    const exactPos = result.exactPosition || {
                        x: result.bounds.left,
                        y: result.bounds.top,
                        width: result.bounds.right - result.bounds.left,
                        height: result.bounds.bottom - result.bounds.top
                    };

                    // 创建简化的文字图层
                    try {
                        console.log(`创建文字图层: "${result.text}" 在位置 (${exactPos.x}, ${exactPos.y})`);

                        await batchPlay([
                            {
                                _obj: "make",
                                _target: [{ _ref: "textLayer" }],
                                using: {
                                    _obj: "textLayer",
                                    textKey: result.text,
                                    textClickPoint: {
                                        _obj: "paint",
                                        horizontal: { _unit: "pixelsUnit", _value: exactPos.x },
                                        vertical: { _unit: "pixelsUnit", _value: exactPos.y }
                                    },
                                    textStyleRange: [{
                                        _obj: "textStyleRange",
                                        from: 0,
                                        to: result.text.length,
                                        textStyle: {
                                            _obj: "textStyle",
                                            fontName: result.fontFamily || "ArialMT",
                                            fontStyleName: result.fontStyle || "Regular",
                                            size: {
                                                _unit: "pointsUnit",
                                                _value: result.fontSize || 24
                                            },
                                            color: {
                                                _obj: "RGBColor",
                                                red: result.color?.r || 0,
                                                green: result.color?.g || 0,
                                                blue: result.color?.b || 0
                                            }
                                        }
                                    }],
                                    paragraphStyleRange: [{
                                        _obj: "paragraphStyleRange",
                                        from: 0,
                                        to: result.text.length,
                                        paragraphStyle: {
                                            _obj: "paragraphStyle",
                                            align: {
                                                _enum: "alignmentType",
                                                _value: result.alignment || "left"
                                            }
                                        }
                                    }]
                                },
                                _isCommand: true
                            }
                        ], { synchronousExecution: true });

                        console.log(`文字图层创建成功: ${result.text}`);

                    } catch (layerError) {
                        console.error(`创建文字图层失败 "${result.text}":`, layerError);
                        // 尝试更简单的方法
                        try {
                            await batchPlay([
                                {
                                    _obj: "make",
                                    _target: [{ _ref: "textLayer" }],
                                    _isCommand: true
                                }
                            ], { synchronousExecution: true });

                            // 设置文字内容
                            await batchPlay([
                                {
                                    _obj: "set",
                                    _target: [{ _ref: "textLayer", _enum: "ordinal", _value: "targetEnum" }],
                                    to: {
                                        _obj: "textLayer",
                                        textKey: result.text
                                    },
                                    _isCommand: true
                                }
                            ], { synchronousExecution: true });

                            console.log(`备用方法创建文字图层成功: ${result.text}`);
                        } catch (fallbackError) {
                            console.error(`备用方法也失败:`, fallbackError);
                            continue; // 跳过这个文字，继续处理下一个
                        }
                    }

                    // 设置图层名称
                    await batchPlay(
                        [
                            {
                                _obj: "set",
                                _target: [
                                    {
                                        _ref: "layer",
                                        _enum: "ordinal",
                                        _value: "targetEnum"
                                    }
                                ],
                                to: {
                                    _obj: "layer",
                                    name: textLayerName
                                },
                                _isCommand: true
                            }
                        ],
                        { synchronousExecution: true }
                    );

                    // 如果有旋转角度，应用旋转
                    if (result.rotation && result.rotation !== 0) {
                        await batchPlay(
                            [
                                {
                                    _obj: "transform",
                                    _target: [
                                        {
                                            _ref: "layer",
                                            _enum: "ordinal",
                                            _value: "targetEnum"
                                        }
                                    ],
                                    freeTransformCenterState: {
                                        _enum: "quadCenterState",
                                        _value: "QCSAverage"
                                    },
                                    angle: {
                                        _unit: "angleUnit",
                                        _value: result.rotation
                                    },
                                    _isCommand: true
                                }
                            ],
                            { synchronousExecution: true }
                        );
                    }

                    // 如果有透明度设置，应用透明度
                    if (result.opacity !== undefined && result.opacity !== 100) {
                        await batchPlay(
                            [
                                {
                                    _obj: "set",
                                    _target: [
                                        {
                                            _ref: "layer",
                                            _enum: "ordinal",
                                            _value: "targetEnum"
                                        }
                                    ],
                                    to: {
                                        _obj: "layer",
                                        opacity: {
                                            _unit: "percentUnit",
                                            _value: result.opacity
                                        }
                                    },
                                    _isCommand: true
                                }
                            ],
                            { synchronousExecution: true }
                        );
                    }

                    // 更新进度
                    updateProgress(90 + (i / recognitionResults.length) * 10);
                }
            } catch (error) {
                console.error('创建文字图层错误:', error);
                showError('创建文字图层失败: ' + error.message);
                removeButtonLoadingState(extractTextBtn);
                hideProgress();
                return;
            }

            // 显示详细的识别结果
            const validResults = recognitionResults.filter(r => r.text && r.text.trim() !== '' && (!r.confidence || r.confidence >= 0.5));
            const processedCount = validResults.length;
            const totalDetected = recognitionResults.length;
            const wasTextRemoved = recognitionResult.processedWithRemoval;

            console.log(`处理结果: 总检测 ${totalDetected} 个，有效 ${processedCount} 个`);

            let successMessage = `文字识别完成！共识别 ${totalDetected} 个文字区域，成功创建 ${processedCount} 个文字图层`;
            if (wasTextRemoved) {
                successMessage += '，原始文字已智能去除';
            }

            // 如果没有创建任何图层，给出提示
            if (processedCount === 0) {
                successMessage = '文字识别完成，但未检测到有效的文字内容。请尝试选择包含清晰文字的图层。';
            }

            showSuccess(successMessage);
            removeButtonLoadingState(extractTextBtn);
            hideProgress();
        } catch (error) {
            console.error('文字识别失败:', error);
            showError('文字识别失败，请重试。' + error.message);
            removeButtonLoadingState(extractTextBtn);
            hideProgress();
        }
    }

    // 处理背景透明化
    async function handleMakeTransparent() {
        try {
            addButtonLoadingState(makeTransparentBtn);
            showProgress('正在处理背景透明化...', 0);

            // 模拟进度
            for (let i = 0; i <= 100; i += 5) {
                await simulateProcess(150);
                updateProgress(i);
            }

            showSuccess('背景透明化完成！');
            removeButtonLoadingState(makeTransparentBtn);
            hideProgress();
        } catch (error) {
            console.error('背景透明化失败:', error);
            showError('背景透明化失败，请重试。');
            removeButtonLoadingState(makeTransparentBtn);
            hideProgress();
        }
    }

    // 处理元素分离
    async function handleSeparateElements() {
        try {
            addButtonLoadingState(separateElementsBtn);
            showProgress('正在分离图像元素...', 0);

            // 模拟进度
            for (let i = 0; i <= 100; i += 4) {
                await simulateProcess(120);
                updateProgress(i);
            }

            showSuccess('元素分离完成！');
            removeButtonLoadingState(separateElementsBtn);
            hideProgress();
        } catch (error) {
            console.error('元素分离失败:', error);
            showError('元素分离失败，请重试。');
            removeButtonLoadingState(separateElementsBtn);
            hideProgress();
        }
    }

    // 设置对话框相关函数
    function handleShowSettings() {
        settingsDialog.style.display = 'flex';
        document.body.style.overflow = 'hidden';

        // 对话框打开动画
        const dialogContent = settingsDialog.querySelector('.dialog-content');
        dialogContent.style.opacity = '0';
        dialogContent.style.transform = 'translateY(20px)';

        setTimeout(() => {
            dialogContent.style.opacity = '1';
            dialogContent.style.transform = 'translateY(0)';
        }, 10);
    }

    function handleCloseSettings() {
        const dialogContent = settingsDialog.querySelector('.dialog-content');
        dialogContent.style.opacity = '0';
        dialogContent.style.transform = 'translateY(20px)';

        setTimeout(() => {
        settingsDialog.style.display = 'none';
        document.body.style.overflow = '';
        }, 200);
    }

    function handleSaveSettings() {
        const settings = {
            accuracy: sliders.accuracy.value,
            enableMultiLanguage: document.getElementById('enableMultiLanguage').checked,
            downsampleLargeImages: document.getElementById('downsampleLargeImages').checked,
            enableMultiThreading: document.getElementById('enableMultiThreading').checked,
            adaptToSystemTheme: document.getElementById('adaptToSystemTheme').checked,
            showAdvancedOptions: document.getElementById('showAdvancedOptions').checked,
            smoothness: sliders.smoothness.value,
            feather: sliders.feather.value,
            preserveOriginal: document.getElementById('preserveOriginal').checked
        };

        localStorage.setItem('pluginSettings', JSON.stringify(settings));
        showSuccess('设置已保存');
        handleCloseSettings();

        // 如果主题设置更改，应用新主题
        detectSystemTheme();
    }

    function handleResetSettings() {
        showConfirmDialog('确定要重置所有设置吗？', () => {
            localStorage.removeItem('pluginSettings');
            loadSettings();
            showSuccess('设置已重置');
        });
    }

    function loadSettings() {
        try {
        const savedSettings = localStorage.getItem('pluginSettings');
            const settings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;

            // 恢复滑块值
            Object.keys(sliders).forEach(key => {
                if (sliders[key] && settings[key] !== undefined) {
                    sliders[key].value = settings[key];
                    updateSliderValue(key);
            }
            });

            // 恢复复选框状态
            if (document.getElementById('enableMultiLanguage')) {
                document.getElementById('enableMultiLanguage').checked = settings.enableMultiLanguage !== undefined ? settings.enableMultiLanguage : defaultSettings.enableMultiLanguage;
            }

            if (document.getElementById('downsampleLargeImages')) {
                document.getElementById('downsampleLargeImages').checked = settings.downsampleLargeImages !== undefined ? settings.downsampleLargeImages : defaultSettings.downsampleLargeImages;
            }

            if (document.getElementById('enableMultiThreading')) {
                document.getElementById('enableMultiThreading').checked = settings.enableMultiThreading !== undefined ? settings.enableMultiThreading : defaultSettings.enableMultiThreading;
            }

            if (document.getElementById('adaptToSystemTheme')) {
                document.getElementById('adaptToSystemTheme').checked = settings.adaptToSystemTheme !== undefined ? settings.adaptToSystemTheme : defaultSettings.adaptToSystemTheme;
            }

            if (document.getElementById('showAdvancedOptions')) {
                document.getElementById('showAdvancedOptions').checked = settings.showAdvancedOptions !== undefined ? settings.showAdvancedOptions : defaultSettings.showAdvancedOptions;
            }

            if (document.getElementById('preserveOriginal')) {
                document.getElementById('preserveOriginal').checked = settings.preserveOriginal !== undefined ? settings.preserveOriginal : defaultSettings.preserveOriginal;
            }

            // 设置高级选项可见性
            if (settings.showAdvancedOptions) {
                if (advancedSection) {
                    advancedSection.querySelector('.advanced-options').style.display = 'block';
                    toggleAdvancedBtn.textContent = '收起';
                }
            } else {
            if (advancedSection) {
                    advancedSection.querySelector('.advanced-options').style.display = 'none';
                    toggleAdvancedBtn.textContent = '展开';
                }
            }
        } catch (error) {
            console.error('加载设置失败:', error);
            // 重置为默认值
            resetToDefaults();
        }
    }

    // 重置为默认设置
    function resetToDefaults() {
        localStorage.removeItem('pluginSettings');

        Object.keys(sliders).forEach(key => {
            if (sliders[key] && defaultSettings[key] !== undefined) {
                sliders[key].value = defaultSettings[key];
                updateSliderValue(key);
            }
        });

        if (document.getElementById('enableMultiLanguage')) {
            document.getElementById('enableMultiLanguage').checked = defaultSettings.enableMultiLanguage;
        }

        if (document.getElementById('downsampleLargeImages')) {
            document.getElementById('downsampleLargeImages').checked = defaultSettings.downsampleLargeImages;
        }

        if (document.getElementById('enableMultiThreading')) {
            document.getElementById('enableMultiThreading').checked = defaultSettings.enableMultiThreading;
        }

        if (document.getElementById('adaptToSystemTheme')) {
            document.getElementById('adaptToSystemTheme').checked = defaultSettings.adaptToSystemTheme;
        }

        if (document.getElementById('showAdvancedOptions')) {
            document.getElementById('showAdvancedOptions').checked = defaultSettings.showAdvancedOptions;
        }

        if (document.getElementById('preserveOriginal')) {
            document.getElementById('preserveOriginal').checked = defaultSettings.preserveOriginal;
        }

        updateBatchButtonState();
    }

    // 获取设置值
    function getSetting(key) {
        try {
            const savedSettings = localStorage.getItem('pluginSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                return settings[key] !== undefined ? settings[key] : defaultSettings[key];
            }
            return defaultSettings[key];
        } catch (error) {
            console.error('获取设置失败:', error);
            return defaultSettings[key];
        }
    }

    // 处理高级选项的显示/隐藏
    function handleToggleAdvanced() {
        const advancedOptions = advancedSection.querySelector('.advanced-options');

        if (advancedOptions.style.display === 'none' || !advancedOptions.style.display) {
            advancedOptions.style.display = 'block';
            toggleAdvancedBtn.textContent = '收起';

            // 保存状态
            const settings = JSON.parse(localStorage.getItem('pluginSettings') || '{}');
            settings.showAdvancedOptions = true;
            localStorage.setItem('pluginSettings', JSON.stringify(settings));
        } else {
            advancedOptions.style.display = 'none';
            toggleAdvancedBtn.textContent = '展开';

            // 保存状态
            const settings = JSON.parse(localStorage.getItem('pluginSettings') || '{}');
            settings.showAdvancedOptions = false;
            localStorage.setItem('pluginSettings', JSON.stringify(settings));
        }
    }

    // 更新滑块值显示
    function updateSliderValue(key) {
        if (sliders[key] && sliderValues[key]) {
            let value = sliders[key].value;

            // 对于小数值，显示一位小数
            if (key === 'feather') {
                value = parseFloat(value).toFixed(1);
            }

            sliderValues[key].textContent = value;

            // 保存设置
            saveSliderValue(key, sliders[key].value);
        }
    }

    // 保存滑块值
    function saveSliderValue(key, value) {
        try {
            const settings = JSON.parse(localStorage.getItem('pluginSettings') || '{}');
            settings[key] = value;
            localStorage.setItem('pluginSettings', JSON.stringify(settings));
        } catch (error) {
            console.error('保存滑块值失败:', error);
        }
    }

    // 更新所有滑块值
    function updateAllSliderValues() {
        Object.keys(sliders).forEach(key => {
            if (sliders[key]) {
                updateSliderValue(key);
            }
        });
    }

    // 添加按钮加载状态
    function addButtonLoadingState(button) {
        if (!button) return;
        button.disabled = true;
        button.classList.add('loading');
        const originalText = button.textContent;
        button.setAttribute('data-original-text', originalText);
        button.innerHTML = `<span class="loading-spinner"></span> 处理中...`;
    }

    // 移除按钮加载状态
    function removeButtonLoadingState(button) {
        if (!button) return;
        button.disabled = false;
        button.classList.remove('loading');
        const originalText = button.getAttribute('data-original-text');
        if (originalText) {
            button.textContent = originalText;
        }
    }

    // 显示进度条
    function showProgress(message, initialProgress = 0) {
        if (progressBar && progressFill && statusText) {
        statusText.textContent = message;
            progressFill.style.width = `${initialProgress}%`;
        progressBar.style.display = 'block';
        }
    }

    // 更新进度
    function updateProgress(percent) {
        if (progressFill) {
        progressFill.style.width = `${percent}%`;
        }
    }

    // 隐藏进度条
    function hideProgress() {
        if (progressBar) {
        setTimeout(() => {
            progressBar.style.display = 'none';
        }, 1000);
        }
    }

    // 显示状态信息
    function showStatus(message) {
        if (statusText) {
        statusText.textContent = message;
            statusText.className = 'status-text';
        }
    }

    // 显示成功信息
    function showSuccess(message) {
        if (statusText) {
            statusText.textContent = message;
            statusText.className = 'status-text success';

            try {
                // 尝试使用animate API，如果支持的话
                if (typeof statusText.animate === 'function') {
                    statusText.animate([
                        { opacity: 0, transform: 'translateY(10px)' },
                        { opacity: 1, transform: 'translateY(0)' }
                    ], {
                        duration: 300,
                        easing: 'ease-out'
                    });
                } else {
                    // 备用方案：使用CSS类触发过渡效果
                    statusText.classList.add('animate-in');
            setTimeout(() => {
                        statusText.classList.remove('animate-in');
                    }, 300);
                }
            } catch (error) {
                console.log('动画API不可用，使用静态显示');
            }
        }
    }

    // 显示错误信息
    function showError(message) {
        if (statusText) {
        statusText.textContent = message;
            statusText.className = 'status-text error';

            try {
                // 尝试使用animate API，如果支持的话
                if (typeof statusText.animate === 'function') {
                    statusText.animate([
                        { opacity: 0, transform: 'translateY(10px)' },
                        { opacity: 1, transform: 'translateY(0)' }
                    ], {
                        duration: 300,
                        easing: 'ease-out'
                    });
                } else {
                    // 备用方案：使用CSS类触发过渡效果
                    statusText.classList.add('animate-in');
        setTimeout(() => {
                        statusText.classList.remove('animate-in');
                    }, 300);
                }
            } catch (error) {
                console.log('动画API不可用，使用静态显示');
            }
        }
    }

    // 显示确认对话框
    function showConfirmDialog(message, onConfirm) {
        // 创建对话框元素
        const confirmDialog = document.createElement('div');
        confirmDialog.className = 'dialog confirm-dialog';
        confirmDialog.style.display = 'flex';

        confirmDialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h3>确认操作</h3>
                    <button class="close-button">&times;</button>
                </div>
                <div class="dialog-body">
                    <p>${message}</p>
                </div>
                <div class="dialog-footer">
                    <button class="sp-button secondary cancel-button">取消</button>
                    <button class="sp-button primary confirm-button">确认</button>
                </div>
            </div>
        `;

        document.body.appendChild(confirmDialog);

        // 添加事件监听器
        const closeButton = confirmDialog.querySelector('.close-button');
        const cancelButton = confirmDialog.querySelector('.cancel-button');
        const confirmButton = confirmDialog.querySelector('.confirm-button');

        closeButton.addEventListener('click', () => {
            document.body.removeChild(confirmDialog);
        });

        cancelButton.addEventListener('click', () => {
            document.body.removeChild(confirmDialog);
        });

        confirmButton.addEventListener('click', () => {
            onConfirm();
            document.body.removeChild(confirmDialog);
        });

        // 点击对话框外部关闭
        confirmDialog.addEventListener('click', (e) => {
            if (e.target === confirmDialog) {
                document.body.removeChild(confirmDialog);
            }
        });
    }

    // 模拟处理过程（仅用于演示）
    function simulateProcess(duration) {
        return new Promise(resolve => {
            setTimeout(resolve, duration);
        });
    }

    // 修改窗口调整大小的处理函数，添加高度处理逻辑
    function addResizeHandling() {
        // 获取所有可能需要调整的UI元素
        const tooltips = document.querySelectorAll('.tooltip');
        const buttons = document.querySelectorAll('.sp-button');
        const sections = document.querySelectorAll('.section');
        const container = document.querySelector('.container');
        const mainContent = document.querySelector('.main-content');

        // 设置最小宽度和高度阈值
        const MIN_PANEL_WIDTH = CONFIG.UI.MIN_PANEL_WIDTH;
        const MIN_PANEL_HEIGHT = CONFIG.UI.MIN_PANEL_HEIGHT;
        const COMPACT_HEIGHT = CONFIG.UI.COMPACT_HEIGHT;

        // 添加resize监听器
        try {
            window.addEventListener('resize', handleResize);
            console.log('已添加窗口大小变化监听器');
        } catch (error) {
            console.log('使用备用窗口大小检测方法');
            // 在UXP环境中，使用定时器定期检查窗口大小
            setInterval(handleResize, 1000);
        }

        // 初始检查一次
        handleResize();

        function handleResize() {
            // 获取窗口尺寸
            let windowWidth = 300;
            let windowHeight = 500;

            try {
                windowWidth = window.innerWidth || document.documentElement.clientWidth || 300;
                windowHeight = window.innerHeight || document.documentElement.clientHeight || 500;
            } catch (error) {
                // 如果无法获取窗口尺寸，使用容器尺寸
                if (container) {
                    windowWidth = container.clientWidth || 300;
                    windowHeight = container.clientHeight || 500;
                }
            }

            // 处理窗口宽度变化
            handleWidthChange(windowWidth);

            // 处理窗口高度变化
            handleHeightChange(windowHeight);

            // 检查内容是否溢出
            checkOverflow();
        }

        function handleWidthChange(windowWidth) {
            // 在非常窄的面板上调整工具提示的位置
            if (windowWidth < MIN_PANEL_WIDTH) {
                tooltips.forEach(tooltip => {
                    const tooltipText = tooltip.querySelector('.tooltip-text');
                    if (tooltipText) {
                        tooltipText.style.left = '0';
                        tooltipText.style.marginLeft = '0';
                        tooltipText.style.right = 'auto';
                        tooltipText.style.width = '160px';
                    }
                });

                // 调整按钮文本，移除过长文本中的非必要词
                buttons.forEach(button => {
                    const originalText = button.getAttribute('data-original-fulltext');
                    if (!originalText) {
                        button.setAttribute('data-original-fulltext', button.textContent);
                    }

                    // 根据按钮内容，在窄屏时使用更简短的文本
                    if (button.textContent.includes('背景透明化') || button.textContent.includes('背景透明')) {
                        button.textContent = '透明化';
                    } else if (button.textContent.includes('元素分离')) {
                        button.textContent = '分离';
                    } else if (button.textContent.includes('提取文字')) {
                        button.textContent = '提取';
                    } else if (button.textContent.includes('开始批量处理')) {
                        button.textContent = '开始处理';
                    }
                });
            } else {
                // 恢复正常宽度的样式
                tooltips.forEach(tooltip => {
                    const tooltipText = tooltip.querySelector('.tooltip-text');
                    if (tooltipText) {
                        tooltipText.style.left = '50%';
                        tooltipText.style.marginLeft = '-90px';
                        tooltipText.style.width = '180px';
                    }
                });

                // 恢复按钮文本
                buttons.forEach(button => {
                    const originalText = button.getAttribute('data-original-fulltext');
                    if (originalText && !button.classList.contains('loading')) {
                        button.textContent = originalText;
                    }
                });
            }
        }

        function handleHeightChange(windowHeight) {
            // 处理窗口高度变化
            if (windowHeight < COMPACT_HEIGHT) {
                // 进入紧凑模式
                document.documentElement.classList.add('compact-mode');

                // 调整主内容区域的滚动
                if (mainContent) {
                    // 计算主内容区域的最大高度，留出状态栏和页脚的空间
                    const statusSectionHeight = document.querySelector('.status-section')?.offsetHeight || 40;
                    const footerHeight = document.querySelector('.footer')?.offsetHeight || 40;
                    const maxContentHeight = windowHeight - statusSectionHeight - footerHeight - 16; // 额外留出一些空间

                    mainContent.style.maxHeight = `${Math.max(180, maxContentHeight)}px`;
                    mainContent.style.overflowY = 'auto';
                    mainContent.style.paddingRight = '0';
                    mainContent.style.marginRight = '0';

                    // 确保滚动条靠右边缘显示
                    const container = document.querySelector('.container');
                    if (container) {
                        container.style.overflowX = 'hidden';
                    }
                }

                // 使用div替代hr作为分隔线，便于更好地控制
                const dividers = document.querySelectorAll('hr.divider');
                dividers.forEach(divider => {
                    const divElement = document.createElement('div');
                    divElement.className = divider.className;
                    divider.parentNode.replaceChild(divElement, divider);
                });

                // 简化高级选项区域
                const advancedSection = document.getElementById('advancedSection');
                if (advancedSection) {
                    // 如果高级选项已展开，则自动折叠
                    const advancedOptions = advancedSection.querySelector('.advanced-options');
                    if (advancedOptions && advancedOptions.style.display !== 'none') {
                        const toggleBtn = document.getElementById('toggleAdvanced');
                        if (toggleBtn && windowHeight < MIN_PANEL_HEIGHT) {
                            toggleBtn.click(); // 自动折叠高级选项
                        }
                    }
                }

                // 调整按钮文本，使其更简短
                buttons.forEach(button => {
                    // 保存原始文本，以便恢复
                    if (!button.hasAttribute('data-original-text')) {
                        button.setAttribute('data-original-text', button.textContent.trim());
                    }

                    // 根据按钮内容，使用更简短的文本
                    const originalText = button.getAttribute('data-original-text');
                    if (originalText.includes('背景透明')) {
                        button.textContent = '透明化';
                    } else if (originalText.includes('元素分离')) {
                        button.textContent = '分离';
                    } else if (originalText.includes('提取文字')) {
                        button.textContent = '提取';
                    }
                });
            } else {
                // 退出紧凑模式
                document.documentElement.classList.remove('compact-mode');

                // 恢复主内容区域
                if (mainContent) {
                    mainContent.style.maxHeight = '';
                    mainContent.style.overflowY = '';

                    // 恢复容器样式
                    const container = document.querySelector('.container');
                    if (container) {
                        container.style.overflowX = '';
                    }
                }

                // 恢复按钮文本
                buttons.forEach(button => {
                    const originalText = button.getAttribute('data-original-text');
                    if (originalText && !button.classList.contains('loading')) {
                        button.textContent = originalText;
                    }
                });
            }
        }

        function checkOverflow() {
            // 检查内容是否溢出
            sections.forEach(section => {
                const header = section.querySelector('.section-header');
                if (header) {
                    const title = header.querySelector('h3');
                    const tooltip = header.querySelector('.tooltip');

                    if (title && tooltip) {
                        const headerWidth = header.offsetWidth;
                        const tooltipWidth = tooltip.offsetWidth;
                        const availableWidth = headerWidth - tooltipWidth - 10;

                        if (title.scrollWidth > availableWidth) {
                            title.style.width = `${availableWidth}px`;
                        } else {
                            title.style.width = 'auto';
                        }
                    }
                }
            });
        }
    }

    // 初始化插件
    initPlugin();
});